'use client'

import React, { useState, useEffect, useRef } from 'react'
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  RectangleStackIcon,
  AdjustmentsHorizontalIcon,
  EyeIcon,
  EyeSlashIcon,
  ChevronDownIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  PowerIcon,
  BuildingOfficeIcon,
  DocumentIcon
} from '@heroicons/react/24/outline'
import { ClientModal } from './client-modal'
import { ClientAvatar } from './client-avatar'
import { CrudConfig } from '../crud/types'
import { motion, AnimatePresence } from 'framer-motion'

interface Client {
  id: string
  companyName: string
  contactName: string
  contactEmail: string
  contactPhone?: string
  website?: string
  industry: string
  companySize: string
  address?: string
  city?: string
  state?: string
  country?: string
  zipCode?: string
  isActive: boolean
  notes?: string
  createdAt: string
  updatedAt: string
  [key: string]: any
}

interface ViewSettings {
  mode: 'list' | 'grid' | 'cards'
  density: 'compact' | 'comfortable'
  visibleColumns: string[]
}

interface ClientsManagerProps {
  config: CrudConfig<Client>
}

export function ClientsManager({ config }: ClientsManagerProps) {
  // State management
  const [clients, setClients] = useState<Client[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')
  const [selectedClients, setSelectedClients] = useState<string[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalCount, setTotalCount] = useState(0)
  const [sortField, setSortField] = useState(config.defaultSort?.field || 'updatedAt')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>(config.defaultSort?.direction || 'desc')
  const [filters, setFilters] = useState<Record<string, string>>({})
  const [actionLoading, setActionLoading] = useState<string | null>(null)

  // Modal states
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editingClient, setEditingClient] = useState<Client | null>(null)

  // View settings
  const [viewSettings, setViewSettings] = useState<ViewSettings>({
    mode: config.defaultViewSettings?.mode || 'list',
    density: config.defaultViewSettings?.density || 'comfortable',
    visibleColumns: config.defaultViewSettings?.visibleColumns || config.columns.map(col => col.key as string)
  })

  // UI states
  const [showFilters, setShowFilters] = useState(false)
  const [showColumnVisibility, setShowColumnVisibility] = useState(false)
  const [showBulkActions, setShowBulkActions] = useState(false)

  const searchInputRef = useRef<HTMLInputElement>(null)

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, 300)

    return () => clearTimeout(timer)
  }, [searchTerm])

  // Fetch clients when dependencies change
  useEffect(() => {
    fetchClients()
  }, [debouncedSearchTerm, currentPage, sortField, sortDirection, filters])

  const fetchClients = async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: (config.pageSize || 10).toString(),
        sortBy: sortField,
        sortOrder: sortDirection,
        ...(debouncedSearchTerm && { search: debouncedSearchTerm }),
        ...filters
      })

      const response = await fetch(`/api/admin/${config.endpoint}?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch clients: ${response.statusText}`)
      }

      const data = await response.json()
      
      if (data.success) {
        setClients(data.data || [])
        setTotalPages(data.pagination?.totalPages || 1)
        setTotalCount(data.pagination?.total || 0)
      } else {
        throw new Error(data.error || 'Failed to fetch clients')
      }
    } catch (err) {
      console.error('Error fetching clients:', err)
      setError(err instanceof Error ? err.message : 'An error occurred')
      setClients([])
    } finally {
      setLoading(false)
    }
  }

  const handleCreate = async (data: any) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create client')
      }

      const result = await response.json()
      
      if (result.success) {
        setIsCreateModalOpen(false)
        await fetchClients()
        // Show success notification
      } else {
        throw new Error(result.error || 'Failed to create client')
      }
    } catch (error) {
      console.error('Error creating client:', error)
      throw error
    }
  }

  const handleUpdate = async (id: string, data: any) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update client')
      }

      const result = await response.json()
      
      if (result.success) {
        setIsEditModalOpen(false)
        setEditingClient(null)
        await fetchClients()
        // Show success notification
      } else {
        throw new Error(result.error || 'Failed to update client')
      }
    } catch (error) {
      console.error('Error updating client:', error)
      throw error
    }
  }

  const handleDelete = async (id: string) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete client')
      }

      const result = await response.json()
      
      if (result.success) {
        await fetchClients()
        // Show success notification
      } else {
        throw new Error(result.error || 'Failed to delete client')
      }
    } catch (error) {
      console.error('Error deleting client:', error)
      throw error
    }
  }

  const handleToggleStatus = async (id: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update client status')
      }

      const result = await response.json()
      
      if (result.success) {
        await fetchClients()
        // Show success notification
      } else {
        throw new Error(result.error || 'Failed to update client status')
      }
    } catch (error) {
      console.error('Error updating client status:', error)
      throw error
    }
  }

  const handleBulkAction = async (action: string, clientIds: string[]) => {
    try {
      let endpoint = `/api/admin/${config.endpoint}`
      let method = 'PUT'
      let body: any = { ids: clientIds }

      switch (action) {
        case 'activate':
          body.data = { isActive: true }
          break
        case 'deactivate':
          body.data = { isActive: false }
          break
        case 'delete':
          method = 'DELETE'
          body = { ids: clientIds }
          break
        default:
          throw new Error(`Unknown bulk action: ${action}`)
      }

      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Failed to ${action} clients`)
      }

      const result = await response.json()
      
      if (result.success) {
        setSelectedClients([])
        await fetchClients()
        // Show success notification
      } else {
        throw new Error(result.error || `Failed to ${action} clients`)
      }
    } catch (error) {
      console.error(`Error performing bulk ${action}:`, error)
      throw error
    }
  }

  // Handle individual actions
  const handleAction = async (action: string, item: any) => {
    const actionKey = `${action}-${item.id}`

    try {
      setActionLoading(actionKey)

      switch (action) {
        case 'view':
          // Open client details in modal or navigate to detail page
          console.log('View client:', item)
          break

        case 'edit':
          setEditingClient(item)
          setIsEditModalOpen(true)
          break

        case 'toggle-status':
          await handleToggleStatus(item.id, !item.isActive)
          break

        case 'delete':
          if (window.confirm('Are you sure you want to delete this client? This action cannot be undone.')) {
            await handleDelete(item.id)
          }
          break

        default:
          console.warn(`Unknown action: ${action}`)
      }
    } finally {
      setActionLoading(null)
    }
  }

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
    setCurrentPage(1)
  }

  const handleFilterChange = (filterKey: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [filterKey]: value
    }))
    setCurrentPage(1)
  }

  const handleSelectAll = () => {
    if (selectedClients.length === clients.length) {
      setSelectedClients([])
    } else {
      setSelectedClients(clients.map(client => client.id))
    }
  }

  const handleSelectClient = (clientId: string) => {
    setSelectedClients(prev =>
      prev.includes(clientId)
        ? prev.filter(id => id !== clientId)
        : [...prev, clientId]
    )
  }

  const clearSearch = () => {
    setSearchTerm('')
    if (searchInputRef.current) {
      searchInputRef.current.focus()
    }
  }

  const renderActionButton = (action: any, item: any) => {
    const isLoading = actionLoading === `${action.action}-${item.id}`
    const IconComponent = getIconComponent(action.icon)

    return (
      <button
        key={action.action}
        onClick={() => handleAction(action.action, item)}
        disabled={isLoading || action.disabled}
        className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-md transition-colors ${getActionButtonClasses(action.variant)} ${
          isLoading || action.disabled ? 'opacity-50 cursor-not-allowed' : ''
        }`}
        title={action.tooltip || action.label}
      >
        {isLoading ? (
          <div className="w-3 h-3 border border-current border-t-transparent rounded-full animate-spin" />
        ) : (
          IconComponent && <IconComponent className="w-3 h-3" />
        )}
      </button>
    )
  }

  const getIconComponent = (iconName: string) => {
    const iconMap: Record<string, any> = {
      'EyeIcon': EyeIcon,
      'PencilIcon': PencilIcon,
      'TrashIcon': TrashIcon,
      'PowerIcon': PowerIcon,
      'BuildingOfficeIcon': BuildingOfficeIcon,
      'DocumentIcon': DocumentIcon
    }
    return iconMap[iconName]
  }

  const getActionButtonClasses = (variant?: string) => {
    switch (variant) {
      case 'primary':
        return 'text-blue-600 hover:text-blue-700 hover:bg-blue-50'
      case 'secondary':
        return 'text-gray-600 hover:text-gray-700 hover:bg-gray-50'
      case 'danger':
        return 'text-red-600 hover:text-red-700 hover:bg-red-50'
      case 'warning':
        return 'text-yellow-600 hover:text-yellow-700 hover:bg-yellow-50'
      case 'success':
        return 'text-green-600 hover:text-green-700 hover:bg-green-50'
      default:
        return 'text-gray-600 hover:text-gray-700 hover:bg-gray-50'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const renderCellContent = (client: Client, column: any) => {
    const value = client[column.key as keyof Client]

    switch (column.renderType) {
      case 'email':
        return value ? (
          <a href={`mailto:${value}`} className="text-blue-600 hover:text-blue-800">
            {value}
          </a>
        ) : '-'

      case 'date':
        return value ? formatDate(value as string) : '-'

      case 'currency':
        return typeof value === 'number' ? formatCurrency(value) : '-'

      case 'boolean':
      case 'status':
        if (column.key === 'isActive') {
          return (
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {value ? 'Active' : 'Inactive'}
            </span>
          )
        }
        return value ? 'Yes' : 'No'

      case 'company':
        return (
          <div className="flex items-center space-x-3">
            <ClientAvatar client={client} size="sm" />
            <div>
              <div className="text-sm font-medium text-gray-900">{value}</div>
              {client.website && (
                <div className="text-sm text-gray-500">{client.website}</div>
              )}
            </div>
          </div>
        )

      default:
        return value || '-'
    }
  }

  const renderTableView = () => (
    <div className="bg-white shadow-sm rounded-lg overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {config.enableBulkActions && (
                <th className="px-6 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={selectedClients.length === clients.length && clients.length > 0}
                    onChange={handleSelectAll}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </th>
              )}
              {config.columns
                .filter(col => viewSettings.visibleColumns.includes(col.key as string))
                .map((column) => (
                  <th
                    key={column.key as string}
                    className={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${
                      column.sortable ? 'cursor-pointer hover:bg-gray-100' : ''
                    }`}
                    onClick={() => column.sortable && handleSort(column.key as string)}
                  >
                    <div className="flex items-center space-x-1">
                      <span>{column.label}</span>
                      {column.sortable && sortField === column.key && (
                        sortDirection === 'asc' ? (
                          <ArrowUpIcon className="w-4 h-4" />
                        ) : (
                          <ArrowDownIcon className="w-4 h-4" />
                        )
                      )}
                    </div>
                  </th>
                ))}
              {config.actions && config.actions.length > 0 && (
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              )}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {clients.map((client) => (
              <tr key={client.id} className="hover:bg-gray-50">
                {config.enableBulkActions && (
                  <td className="px-6 py-4">
                    <input
                      type="checkbox"
                      checked={selectedClients.includes(client.id)}
                      onChange={() => handleSelectClient(client.id)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </td>
                )}
                {config.columns
                  .filter(col => viewSettings.visibleColumns.includes(col.key as string))
                  .map((column) => (
                    <td
                      key={column.key as string}
                      className={`px-6 py-4 whitespace-nowrap text-sm ${
                        viewSettings.density === 'compact' ? 'py-2' : 'py-4'
                      }`}
                    >
                      {renderCellContent(client, column)}
                    </td>
                  ))}
                {config.actions && config.actions.length > 0 && (
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <div className="flex items-center space-x-2">
                      {config.actions.map((action) => renderActionButton(action, client))}
                    </div>
                  </td>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )

  const renderGridView = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {clients.map((client) => (
        <motion.div
          key={client.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
        >
          <div className="flex items-center space-x-3 mb-4">
            <ClientAvatar client={client} size="md" />
            <div className="flex-1 min-w-0">
              <h3 className="text-sm font-medium text-gray-900 truncate">
                {client.companyName}
              </h3>
              <p className="text-sm text-gray-500 truncate">
                {client.contactName}
              </p>
            </div>
            {config.enableBulkActions && (
              <input
                type="checkbox"
                checked={selectedClients.includes(client.id)}
                onChange={() => handleSelectClient(client.id)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            )}
          </div>

          <div className="space-y-2 mb-4">
            <div className="flex items-center text-sm text-gray-600">
              <span className="font-medium">Email:</span>
              <span className="ml-2 truncate">{client.contactEmail || '-'}</span>
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <span className="font-medium">Industry:</span>
              <span className="ml-2">{client.industry || '-'}</span>
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <span className="font-medium">Status:</span>
              <span className={`ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                client.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {client.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>

          {config.actions && config.actions.length > 0 && (
            <div className="flex items-center justify-end space-x-2 pt-4 border-t border-gray-200">
              {config.actions.map((action) => renderActionButton(action, client))}
            </div>
          )}
        </motion.div>
      ))}
    </div>
  )

  const renderCardsView = () => (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {clients.map((client) => (
        <motion.div
          key={client.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
        >
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center space-x-3">
              <ClientAvatar client={client} size="lg" />
              <div>
                <h3 className="text-lg font-medium text-gray-900">
                  {client.companyName}
                </h3>
                <p className="text-sm text-gray-500">
                  {client.contactName} • {client.industry}
                </p>
              </div>
            </div>
            {config.enableBulkActions && (
              <input
                type="checkbox"
                checked={selectedClients.includes(client.id)}
                onChange={() => handleSelectClient(client.id)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            )}
          </div>

          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <span className="text-sm font-medium text-gray-500">Email</span>
              <p className="text-sm text-gray-900 truncate">{client.contactEmail || '-'}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-500">Phone</span>
              <p className="text-sm text-gray-900">{client.contactPhone || '-'}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-500">Company Size</span>
              <p className="text-sm text-gray-900">{client.companySize || '-'}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-500">Status</span>
              <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                client.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {client.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>

          {client.notes && (
            <div className="mb-4">
              <span className="text-sm font-medium text-gray-500">Notes</span>
              <p className="text-sm text-gray-900 mt-1 line-clamp-2">{client.notes}</p>
            </div>
          )}

          {config.actions && config.actions.length > 0 && (
            <div className="flex items-center justify-end space-x-2 pt-4 border-t border-gray-200">
              {config.actions.map((action) => renderActionButton(action, client))}
            </div>
          )}
        </motion.div>
      ))}
    </div>
  )

  if (loading && clients.length === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-500">Loading clients...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center">
          <XMarkIcon className="w-5 h-5 text-red-400 mr-2" />
          <p className="text-red-800">{error}</p>
        </div>
        <button
          onClick={fetchClients}
          className="mt-4 bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors"
        >
          Try Again
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{config.title}</h1>
          {config.description && (
            <p className="mt-1 text-sm text-gray-500">{config.description}</p>
          )}
        </div>
        {config.permissions.create && (
          <button
            onClick={() => setIsCreateModalOpen(true)}
            className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <PlusIcon className="w-4 h-4 mr-2" />
            Add Client
          </button>
        )}
      </div>

      {/* Search and Controls */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          {/* Search */}
          {config.enableSearch && (
            <div className="flex-1 max-w-md">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  ref={searchInputRef}
                  type="text"
                  placeholder={config.searchPlaceholder || 'Search clients...'}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
                {searchTerm && (
                  <button
                    onClick={clearSearch}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    <XMarkIcon className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>
          )}

          {/* Controls */}
          <div className="flex items-center space-x-4">
            {/* Filters */}
            {config.enableFilters && config.filters && config.filters.length > 0 && (
              <div className="relative">
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <FunnelIcon className="w-4 h-4 mr-2" />
                  Filters
                  {Object.values(filters).some(v => v) && (
                    <span className="ml-2 bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded-full">
                      {Object.values(filters).filter(v => v).length}
                    </span>
                  )}
                </button>
              </div>
            )}

            {/* View Controls */}
            {config.enableViewControls && (
              <div className="flex items-center space-x-1 border border-gray-300 rounded-md">
                <button
                  onClick={() => setViewSettings(prev => ({ ...prev, mode: 'list' }))}
                  className={`p-2 text-sm ${
                    viewSettings.mode === 'list'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                  title="List View"
                >
                  <ListBulletIcon className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewSettings(prev => ({ ...prev, mode: 'grid' }))}
                  className={`p-2 text-sm ${
                    viewSettings.mode === 'grid'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                  title="Grid View"
                >
                  <Squares2X2Icon className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewSettings(prev => ({ ...prev, mode: 'cards' }))}
                  className={`p-2 text-sm ${
                    viewSettings.mode === 'cards'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                  title="Cards View"
                >
                  <RectangleStackIcon className="w-4 h-4" />
                </button>
              </div>
            )}

            {/* Density Controls */}
            {config.enableDensityControls && (
              <div className="relative">
                <button
                  onClick={() => setViewSettings(prev => ({
                    ...prev,
                    density: prev.density === 'compact' ? 'comfortable' : 'compact'
                  }))}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                  title={`Switch to ${viewSettings.density === 'compact' ? 'comfortable' : 'compact'} view`}
                >
                  <AdjustmentsHorizontalIcon className="w-4 h-4 mr-2" />
                  {viewSettings.density === 'compact' ? 'Compact' : 'Comfortable'}
                </button>
              </div>
            )}

            {/* Column Visibility */}
            {config.enableColumnVisibility && viewSettings.mode === 'list' && (
              <div className="relative">
                <button
                  onClick={() => setShowColumnVisibility(!showColumnVisibility)}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <EyeIcon className="w-4 h-4 mr-2" />
                  Columns
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Filters Dropdown */}
        {showFilters && config.filters && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg border">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {config.filters.map((filter) => (
                <div key={filter.name}>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {filter.label}
                  </label>
                  {filter.type === 'select' ? (
                    <select
                      value={filters[filter.name] || ''}
                      onChange={(e) => handleFilterChange(filter.name, e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    >
                      {filter.options?.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  ) : (
                    <input
                      type={filter.type}
                      value={filters[filter.name] || ''}
                      onChange={(e) => handleFilterChange(filter.name, e.target.value)}
                      placeholder={filter.placeholder}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Column Visibility Dropdown */}
        {showColumnVisibility && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg border">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Show/Hide Columns</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
              {config.columns.map((column) => (
                <label key={column.key as string} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={viewSettings.visibleColumns.includes(column.key as string)}
                    onChange={(e) => {
                      const columnKey = column.key as string
                      setViewSettings(prev => ({
                        ...prev,
                        visibleColumns: e.target.checked
                          ? [...prev.visibleColumns, columnKey]
                          : prev.visibleColumns.filter(col => col !== columnKey)
                      }))
                    }}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2"
                  />
                  <span className="text-sm text-gray-700">{column.label}</span>
                </label>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Bulk Actions */}
      {config.enableBulkActions && selectedClients.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <span className="text-sm font-medium text-blue-900">
                {selectedClients.length} client{selectedClients.length !== 1 ? 's' : ''} selected
              </span>
            </div>
            <div className="flex items-center space-x-2">
              {config.bulkActions?.map((action) => (
                <button
                  key={action.action}
                  onClick={() => {
                    if (action.requiresConfirmation) {
                      if (window.confirm(action.confirmationMessage || `Are you sure you want to ${action.action} the selected clients?`)) {
                        handleBulkAction(action.action, selectedClients)
                      }
                    } else {
                      handleBulkAction(action.action, selectedClients)
                    }
                  }}
                  className={`inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md ${
                    action.variant === 'danger'
                      ? 'text-red-700 bg-red-100 hover:bg-red-200'
                      : action.variant === 'warning'
                      ? 'text-yellow-700 bg-yellow-100 hover:bg-yellow-200'
                      : action.variant === 'success'
                      ? 'text-green-700 bg-green-100 hover:bg-green-200'
                      : 'text-blue-700 bg-blue-100 hover:bg-blue-200'
                  }`}
                >
                  {action.label}
                </button>
              ))}
              <button
                onClick={() => setSelectedClients([])}
                className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                Clear Selection
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      {clients.length === 0 && !loading ? (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
          <BuildingOfficeIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No clients found</h3>
          <p className="text-gray-500 mb-6">
            {searchTerm || Object.values(filters).some(v => v)
              ? 'Try adjusting your search or filters'
              : 'Get started by adding your first client'}
          </p>
          {config.permissions.create && (
            <button
              onClick={() => setIsCreateModalOpen(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
            >
              <PlusIcon className="w-4 h-4 mr-2" />
              Add Client
            </button>
          )}
        </div>
      ) : (
        <div>
          {viewSettings.mode === 'list' && renderTableView()}
          {viewSettings.mode === 'grid' && renderGridView()}
          {viewSettings.mode === 'cards' && renderCardsView()}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 rounded-lg shadow-sm">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing{' '}
                <span className="font-medium">
                  {((currentPage - 1) * (config.pageSize || 10)) + 1}
                </span>{' '}
                to{' '}
                <span className="font-medium">
                  {Math.min(currentPage * (config.pageSize || 10), totalCount)}
                </span>{' '}
                of{' '}
                <span className="font-medium">{totalCount}</span>{' '}
                results
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum
                  if (totalPages <= 5) {
                    pageNum = i + 1
                  } else if (currentPage <= 3) {
                    pageNum = i + 1
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i
                  } else {
                    pageNum = currentPage - 2 + i
                  }

                  return (
                    <button
                      key={pageNum}
                      onClick={() => setCurrentPage(pageNum)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        currentPage === pageNum
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      {pageNum}
                    </button>
                  )
                })}
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Create Modal */}
      <ClientModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreate}
        title="Create Client"
        fields={config.fields}
        layout={config.formLayout}
      />

      {/* Edit Modal */}
      <ClientModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false)
          setEditingClient(null)
        }}
        onSubmit={(data) => editingClient && handleUpdate(editingClient.id, data)}
        title="Edit Client"
        initialData={editingClient}
        fields={config.fields}
        layout={config.formLayout}
      />
    </div>
  )
}
