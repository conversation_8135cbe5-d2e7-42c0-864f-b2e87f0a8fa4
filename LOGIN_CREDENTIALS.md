# 🔑 ACTUAL Database Login Credentials

## Admin Users (Can access admin dashboard)

| Email | Password | Role |
|-------|----------|------|
| `<EMAIL>` | `admin123` | ADMIN |
| `<EMAIL>` | `admin123` | ADMIN |
| `<EMAIL>` | `admin123` | ADMIN |

## Regular Users (Limited access)

| Email | Password | Role |
|-------|----------|------|
| `<EMAIL>` | `user123` | USER |
| `<EMAIL>` | `user123` | USER |
| `<EMAIL>` | `user123` | USER |

**⚠️ These are the ACTUAL passwords stored in the database - verified by bcrypt comparison**

## Usage

1. Go to: `http://localhost:3000/auth/signin`
2. Use any of the admin credentials above to access the admin dashboard
3. Regular users will be denied access to admin areas

## Features

- ✅ Database-only authentication
- ✅ Role-based access control
- ✅ Rate limiting (5 attempts per 15 minutes)
- ✅ Audit logging
- ✅ Clean floating labels
- ✅ Dark/light mode support
- ✅ Responsive design
