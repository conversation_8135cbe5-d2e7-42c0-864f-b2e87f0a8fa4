# 🔑 VERIFIED PostgreSQL Database Credentials

## Admin Users (Full dashboard access)

| Email | Password | Role | Status |
|-------|----------|------|--------|
| `<EMAIL>` | `admin123` | ADMIN | ✅ Verified |
| `<EMAIL>` | `admin123` | ADMIN | ✅ Verified |
| `<EMAIL>` | `admin123` | ADMIN | ✅ Verified |

## Regular Users (Limited access)

| Email | Password | Role | Status |
|-------|----------|------|--------|
| `<EMAIL>` | `user123` | USER | ✅ Verified |
| `<EMAIL>` | `user123` | USER | ✅ Verified |
| `<EMAIL>` | `user123` | USER | ✅ Verified |

**✅ These are the EXACT credentials from your PostgreSQL database**
**🔐 Passwords verified using bcrypt comparison against actual database hashes**

## Usage

1. Go to: `http://localhost:3000/auth/signin`
2. Use any of the admin credentials above to access the admin dashboard
3. Regular users will be denied access to admin areas

## Features

- ✅ Database-only authentication
- ✅ Role-based access control
- ✅ Rate limiting (5 attempts per 15 minutes)
- ✅ Audit logging
- ✅ Clean floating labels
- ✅ Dark/light mode support
- ✅ Responsive design
